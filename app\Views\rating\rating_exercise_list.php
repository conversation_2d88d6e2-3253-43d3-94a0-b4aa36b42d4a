<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="card mb-4 border-start border-navy border-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="h4 fw-bold text-gray-800 mb-1">Rating - Select Exercise</h2>
                    <p class="text-muted mb-0">Select an exercise to rate applications</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Exercises List -->
    <div class="card shadow-sm">
        <div class="card-header bg-white py-3">
            <h5 class="card-title mb-0">Available Exercises for Rating</h5>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="table-light">
                        <tr>
                            <th class="px-4">Exercise Name</th>
                            <th>Exercise ID</th>
                            <th>Created Date</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($exercises)): ?>
                            <tr>
                                <td colspan="5" class="text-center py-4">No exercises found for rating</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($exercises as $exercise): ?>
                                <tr>
                                    <td class="px-4 fw-medium"><?= esc($exercise['exercise_name']) ?></td>
                                    <td><?= esc($exercise['id']) ?></td>
                                    <td><?= date('d M Y', strtotime($exercise['created_at'])) ?></td>
                                    <td>
                                        <span class="badge bg-primary bg-opacity-10 text-primary">
                                            <?= ucfirst($exercise['status']) ?>
                                        </span>
                                    </td>
                                    <td>
                                        <a href="<?= base_url('rating/position-groups/' . $exercise['id']) ?>"
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-arrow-right me-1"></i> Select
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Rating exercises page loaded');
});
</script>
<?= $this->endSection() ?>