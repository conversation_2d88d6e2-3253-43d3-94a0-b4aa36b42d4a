<?php
/**
 * View file for listing applications for shortlisting
 *
 * @var array $exercise Exercise details
 * @var array $position Position details
 * @var array $applications List of applications for the position
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('shortlisting') ?>">Shortlisting</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('shortlisting/positions/' . $exercise['id']) ?>">
                            <?= esc($exercise['exercise_name']) ?>
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?= esc($position['designation']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-md-8">
            <h2><i class="fas fa-user-check me-2"></i>Applications for Shortlisting</h2>
            <div class="card bg-light">
                <div class="card-body py-2">
                    <div class="row">
                        <div class="col-md-6">
                            <strong>Position:</strong> <?= esc($position['designation']) ?><br>
                            <strong>Reference:</strong> <?= esc($position['reference']) ?>
                        </div>
                        <div class="col-md-6">
                            <strong>Classification:</strong> <?= esc($position['classification']) ?><br>
                            <strong>Location:</strong> <?= esc($position['location']) ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= base_url('shortlisting/positions/' . $exercise['id']) ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Positions
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-body">
            <?php if (empty($applications)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i> No applications found for this position.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table id="applicationsTable" class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th width="5%">Rank</th>
                                <th width="20%">Name</th>
                                <th width="15%">Application No.</th>
                                <th width="15%">Current Position</th>
                                <th width="10%">Total Rating</th>
                                <th width="15%">Status</th>
                                <th width="20%">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $rank = 1; ?>
                            <?php foreach ($applications as $application): ?>
                                <tr>
                                    <td><?= $rank++ ?></td>
                                    <td>
                                        <strong><?= esc($application['first_name'] . ' ' . $application['last_name']) ?></strong>
                                        <br>
                                        <small class="text-muted">
                                            <?= esc($application['gender']) ?> | 
                                            <?= date('M d, Y', strtotime($application['date_of_birth'])) ?>
                                        </small>
                                    </td>
                                    <td><?= esc($application['application_number']) ?></td>
                                    <td>
                                        <?= esc($application['current_position']) ?>
                                        <br>
                                        <small class="text-muted"><?= esc($application['current_employer']) ?></small>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?= $application['rating_total'] ?>/<?= $application['rating_max'] ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        $statusClass = 'secondary';
                                        $statusText = 'Pending';
                                        
                                        switch($application['shortlist_status']) {
                                            case 'shortlisted':
                                                $statusClass = 'success';
                                                $statusText = 'Shortlisted';
                                                break;
                                            case 'eliminated':
                                                $statusClass = 'danger';
                                                $statusText = 'Eliminated';
                                                break;
                                            case 'withdrawn':
                                                $statusClass = 'warning';
                                                $statusText = 'Withdrawn';
                                                break;
                                        }
                                        ?>
                                        <span class="badge bg-<?= $statusClass ?>"><?= $statusText ?></span>
                                    </td>
                                    <td>
                                        <a href="<?= base_url('shortlisting/detail/' . $application['id']) ?>" 
                                           class="btn btn-primary btn-sm">
                                            <i class="fas fa-edit me-1"></i> Shortlist
                                        </a>
                                        <button type="button" class="btn btn-info btn-sm" 
                                                onclick="viewApplication(<?= $application['id'] ?>)">
                                            <i class="fas fa-eye me-1"></i> View
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#applicationsTable').DataTable({
        responsive: true,
        order: [[4, 'desc']], // Sort by rating descending
        language: {
            search: "Search applications:",
            lengthMenu: "Show _MENU_ applications per page",
            info: "Showing _START_ to _END_ of _TOTAL_ applications",
            emptyTable: "No applications found for this position",
        }
    });
});

function viewApplication(applicationId) {
    // Mock function for viewing application details
    toastr.info('Application details view functionality will be implemented later.');
}
</script>
<?= $this->endSection() ?>
