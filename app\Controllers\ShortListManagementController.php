<?php

namespace App\Controllers;

use CodeIgniter\RESTful\ResourceController;

class ShortListManagementController extends ResourceController
{
    protected $helpers = ['form', 'url', 'info'];
    protected $session;

    public function __construct()
    {
        $this->session = session();
        helper(['form', 'url', 'info']);
    }

    /**
     * [GET] Display exercises in selection status for shortlisting
     * URI: /shortlisting
     */
    public function index()
    {
        $data = [
            'title' => 'Shortlisting Management',
            'menu' => 'shortlisting',
            'exercises' => $this->getMockExercises()
        ];

        return view('application_shortlist/application_shortlist_index', $data);
    }

    /**
     * [GET] Display positions for a specific exercise
     * URI: /shortlisting/positions/{exercise_id}
     */
    public function positions($exerciseId)
    {
        $exercise = $this->getMockExerciseById($exerciseId);
        if (!$exercise) {
            return redirect()->to(base_url('shortlisting'))
                ->with('error', 'Exercise not found.');
        }

        $data = [
            'title' => 'Positions - Shortlisting',
            'menu' => 'shortlisting',
            'exercise' => $exercise,
            'positions' => $this->getMockPositionsByExercise($exerciseId)
        ];

        return view('application_shortlist/application_shortlist_positions', $data);
    }

    /**
     * [GET] Display applications for a specific position
     * URI: /shortlisting/applications/{position_id}
     */
    public function applications($positionId)
    {
        $position = $this->getMockPositionById($positionId);
        if (!$position) {
            return redirect()->to(base_url('shortlisting'))
                ->with('error', 'Position not found.');
        }

        $exercise = $this->getMockExerciseById($position['exercise_id']);

        $data = [
            'title' => 'Applications - Shortlisting',
            'menu' => 'shortlisting',
            'exercise' => $exercise,
            'position' => $position,
            'applications' => $this->getMockApplicationsByPosition($positionId)
        ];

        return view('application_shortlist/application_shortlist_applications', $data);
    }

    /**
     * [GET] Display shortlisting detail form for a specific application
     * URI: /shortlisting/detail/{application_id}
     */
    public function detail($applicationId)
    {
        $application = $this->getMockApplicationById($applicationId);
        if (!$application) {
            return redirect()->to(base_url('shortlisting'))
                ->with('error', 'Application not found.');
        }

        $position = $this->getMockPositionById($application['position_id']);
        $exercise = $this->getMockExerciseById($position['exercise_id']);

        $data = [
            'title' => 'Shortlist Application',
            'menu' => 'shortlisting',
            'exercise' => $exercise,
            'position' => $position,
            'application' => $application
        ];

        return view('application_shortlist/application_shortlist_detail', $data);
    }

    /**
     * [POST] Update shortlisting status for an application
     * URI: /shortlisting/update/{application_id}
     */
    public function updateStatus($applicationId = null)
    {
        $application = $this->getMockApplicationById($applicationId);
        if (!$application) {
            return redirect()->to(base_url('shortlisting'))
                ->with('error', 'Application not found.');
        }

        // Validate input
        $rules = [
            'shortlist_status' => 'required|in_list[shortlisted,eliminated,withdrawn]',
            'remarks' => 'permit_empty|string|max_length[1000]'
        ];

        if (!$this->validate($rules)) {
            return redirect()->back()
                ->withInput()
                ->with('error', 'Please correct the validation errors.');
        }

        // Mock data for update (not actually saving to database)
        $updateData = [
            'shortlist_status' => $this->request->getPost('shortlist_status'),
            'shortlisted_by' => $this->session->get('user_id') ?? 1,
            'shortlisted_at' => date('Y-m-d H:i:s'),
            'remarks' => trim($this->request->getPost('remarks')),
            'updated_by' => $this->session->get('user_id') ?? 1
        ];

        // Log the mock update
        log_message('info', 'Mock shortlisting update for application ID: ' . $applicationId . ' with data: ' . json_encode($updateData));

        $position = $this->getMockPositionById($application['position_id']);

        return redirect()->to(base_url('shortlisting/applications/' . $position['id']))
            ->with('success', 'Application shortlisting status updated successfully (mock mode).');
    }

    /**
     * Get mock exercises data for UI development
     */
    private function getMockExercises()
    {
        return [
            [
                'id' => 1,
                'exercise_name' => 'IT Department Recruitment 2024',
                'advertisement_no' => 'ADV-2024-001',
                'gazzetted_no' => 'GAZ-2024-001',
                'publish_date_from' => '2024-01-15',
                'publish_date_to' => '2024-12-31',
                'status' => 'selection'
            ],
            [
                'id' => 2,
                'exercise_name' => 'Finance Department Recruitment 2024',
                'advertisement_no' => 'ADV-2024-002',
                'gazzetted_no' => 'GAZ-2024-002',
                'publish_date_from' => '2024-02-01',
                'publish_date_to' => '2024-11-30',
                'status' => 'selection'
            ],
            [
                'id' => 3,
                'exercise_name' => 'Health Department Recruitment 2024',
                'advertisement_no' => 'ADV-2024-003',
                'gazzetted_no' => 'GAZ-2024-003',
                'publish_date_from' => '2024-03-01',
                'publish_date_to' => '2024-10-31',
                'status' => 'selection'
            ]
        ];
    }

    /**
     * Get mock exercise by ID
     */
    private function getMockExerciseById($id)
    {
        $exercises = $this->getMockExercises();
        foreach ($exercises as $exercise) {
            if ($exercise['id'] == $id) {
                return $exercise;
            }
        }
        return null;
    }

    /**
     * Get mock positions by exercise ID
     */
    private function getMockPositionsByExercise($exerciseId)
    {
        $allPositions = [
            [
                'id' => 1,
                'exercise_id' => 1,
                'position_group_id' => 1,
                'position_group_name' => 'Information Technology',
                'designation' => 'Senior Software Developer',
                'reference' => 'IT-001',
                'classification' => 'Level 8',
                'annual_salary' => '85000',
                'location' => 'Port Moresby',
                'total_applications' => 45
            ],
            [
                'id' => 2,
                'exercise_id' => 1,
                'position_group_id' => 1,
                'position_group_name' => 'Information Technology',
                'designation' => 'Database Administrator',
                'reference' => 'IT-002',
                'classification' => 'Level 7',
                'annual_salary' => '75000',
                'location' => 'Port Moresby',
                'total_applications' => 32
            ],
            [
                'id' => 3,
                'exercise_id' => 2,
                'position_group_id' => 2,
                'position_group_name' => 'Finance',
                'designation' => 'Financial Analyst',
                'reference' => 'FIN-001',
                'classification' => 'Level 6',
                'annual_salary' => '65000',
                'location' => 'Port Moresby',
                'total_applications' => 28
            ]
        ];

        return array_filter($allPositions, function($position) use ($exerciseId) {
            return $position['exercise_id'] == $exerciseId;
        });
    }

    /**
     * Get mock position by ID
     */
    private function getMockPositionById($id)
    {
        $allPositions = $this->getMockPositionsByExercise(1);
        $allPositions = array_merge($allPositions, $this->getMockPositionsByExercise(2));
        $allPositions = array_merge($allPositions, $this->getMockPositionsByExercise(3));

        foreach ($allPositions as $position) {
            if ($position['id'] == $id) {
                return $position;
            }
        }
        return null;
    }

    /**
     * Get mock applications by position ID
     */
    private function getMockApplicationsByPosition($positionId)
    {
        // Base applications that will be filtered by position
        $baseApplications = [
            // Position 1 applications (Senior Software Developer)
            [
                'id' => 1,
                'position_id' => 1,
                'application_number' => 'APP-2024-001',
                'first_name' => 'John',
                'last_name' => 'Doe',
                'gender' => 'Male',
                'date_of_birth' => '1990-05-15',
                'citizenship' => 'Papua New Guinea',
                'current_employer' => 'Tech Solutions PNG',
                'current_position' => 'Software Developer',
                'rating_total' => 85,
                'rating_max' => 100,
                'shortlist_status' => 'pending',
                'remarks' => null,
                'created_at' => '2024-01-20 10:30:00'
            ],
            [
                'id' => 2,
                'position_id' => 1,
                'application_number' => 'APP-2024-002',
                'first_name' => 'Jane',
                'last_name' => 'Smith',
                'gender' => 'Female',
                'date_of_birth' => '1988-08-22',
                'citizenship' => 'Papua New Guinea',
                'current_employer' => 'Digital PNG Ltd',
                'current_position' => 'Senior Developer',
                'rating_total' => 92,
                'rating_max' => 100,
                'shortlist_status' => 'shortlisted',
                'remarks' => 'Excellent technical skills',
                'created_at' => '2024-01-21 14:15:00'
            ],
            [
                'id' => 3,
                'position_id' => 1,
                'application_number' => 'APP-2024-003',
                'first_name' => 'Michael',
                'last_name' => 'Johnson',
                'gender' => 'Male',
                'date_of_birth' => '1985-12-10',
                'citizenship' => 'Papua New Guinea',
                'current_employer' => 'PNG Systems Ltd',
                'current_position' => 'Lead Developer',
                'rating_total' => 88,
                'rating_max' => 100,
                'shortlist_status' => 'shortlisted',
                'remarks' => 'Strong leadership experience',
                'created_at' => '2024-01-22 09:45:00'
            ],
            [
                'id' => 4,
                'position_id' => 1,
                'application_number' => 'APP-2024-004',
                'first_name' => 'Sarah',
                'last_name' => 'Wilson',
                'gender' => 'Female',
                'date_of_birth' => '1992-03-18',
                'citizenship' => 'Papua New Guinea',
                'current_employer' => 'Innovation Hub PNG',
                'current_position' => 'Software Engineer',
                'rating_total' => 78,
                'rating_max' => 100,
                'shortlist_status' => 'eliminated',
                'remarks' => 'Insufficient experience for senior role',
                'created_at' => '2024-01-23 16:20:00'
            ],
            [
                'id' => 5,
                'position_id' => 1,
                'application_number' => 'APP-2024-005',
                'first_name' => 'David',
                'last_name' => 'Brown',
                'gender' => 'Male',
                'date_of_birth' => '1987-07-25',
                'citizenship' => 'Papua New Guinea',
                'current_employer' => 'Code Masters PNG',
                'current_position' => 'Senior Software Engineer',
                'rating_total' => 90,
                'rating_max' => 100,
                'shortlist_status' => 'shortlisted',
                'remarks' => 'Outstanding technical competency',
                'created_at' => '2024-01-24 11:10:00'
            ],
            // Position 2 applications (Database Administrator)
            [
                'id' => 6,
                'position_id' => 2,
                'application_number' => 'APP-2024-006',
                'first_name' => 'Lisa',
                'last_name' => 'Anderson',
                'gender' => 'Female',
                'date_of_birth' => '1989-11-08',
                'citizenship' => 'Papua New Guinea',
                'current_employer' => 'Data Solutions PNG',
                'current_position' => 'Database Analyst',
                'rating_total' => 82,
                'rating_max' => 100,
                'shortlist_status' => 'pending',
                'remarks' => null,
                'created_at' => '2024-01-25 13:30:00'
            ],
            [
                'id' => 7,
                'position_id' => 2,
                'application_number' => 'APP-2024-007',
                'first_name' => 'Robert',
                'last_name' => 'Taylor',
                'gender' => 'Male',
                'date_of_birth' => '1986-04-14',
                'citizenship' => 'Papua New Guinea',
                'current_employer' => 'Enterprise Systems PNG',
                'current_position' => 'Senior DBA',
                'rating_total' => 95,
                'rating_max' => 100,
                'shortlist_status' => 'shortlisted',
                'remarks' => 'Exceptional database expertise',
                'created_at' => '2024-01-26 08:45:00'
            ]
        ];

        return array_filter($baseApplications, function($app) use ($positionId) {
            return $app['position_id'] == $positionId;
        });
    }

    /**
     * Get mock application by ID
     */
    private function getMockApplicationById($id)
    {
        $allApplications = $this->getMockApplicationsByPosition(1);
        $allApplications = array_merge($allApplications, $this->getMockApplicationsByPosition(2));
        $allApplications = array_merge($allApplications, $this->getMockApplicationsByPosition(3));

        foreach ($allApplications as $application) {
            if ($application['id'] == $id) {
                return $application;
            }
        }
        return null;
    }
}
