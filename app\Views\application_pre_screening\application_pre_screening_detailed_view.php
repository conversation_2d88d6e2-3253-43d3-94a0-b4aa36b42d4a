<?php
/**
 * Detailed view for application pre-screening
 * Shows application details alongside pre-screening criteria
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('application_pre_screening/exercises') ?>">Exercises</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('application_pre_screening/exercise/' . $exercise['id'] . '/positions') ?>">
                            <?= esc($exercise['exercise_name']) ?>
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('application_pre_screening/position_applications/' . $position['id']) ?>">
                            <?= esc($position['designation']) ?> Applications
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Pre-Screen: <?= esc($application['application_number']) ?>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Header Section -->
    <div class="row mb-3">
        <div class="col-md-8">
            <h2><i class="fas fa-clipboard-check me-2"></i>Application Pre-Screening</h2>
            <p class="text-muted">
                Review application details and complete pre-screening assessment<br>
                <small>Application: <strong><?= esc($application['application_number']) ?></strong> | Position: <strong><?= esc($position['designation']) ?></strong></small>
            </p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= base_url('application_pre_screening/position_applications/' . $position['id']) ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Applications
            </a>
        </div>
    </div>

    <!-- Summary Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Application Summary</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>Status:</strong>
                            <span class="badge <?= ($application['pre_screened_status'] ?? '') === 'passed' ? 'bg-success' :
                                (($application['pre_screened_status'] ?? '') === 'failed' ? 'bg-danger' : 'bg-warning') ?>">
                                <?= ucfirst($application['pre_screened_status'] ?? 'Pending') ?>
                            </span>
                        </div>
                        <div class="col-md-3">
                            <strong>Submitted:</strong>
                            <?= date('M d, Y', strtotime($application['created_at'])) ?>
                        </div>
                        <div class="col-md-3">
                            <strong>Position:</strong>
                            <?= esc($position['designation'] ?? 'N/A') ?>
                        </div>
                        <div class="col-md-3">
                            <strong>Exercise:</strong>
                            <?= esc($exercise['exercise_name'] ?? 'N/A') ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Application Details Section (Left side) -->
        <div class="col-lg-8">
            <!-- Personal Information -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-user me-2"></i>Personal Information</h5>
                    <span class="badge bg-light text-dark">
                        Application #<?= $application['application_number'] ?>
                    </span>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3 text-center mb-3">
                            <?php if (!empty($application['id_photo_path'])): ?>
                                <img src="<?= base_url('public/' . $application['id_photo_path']) ?>"
                                     alt="Applicant Photo" class="img-thumbnail mb-2"
                                     style="max-width: 150px;">
                            <?php else: ?>
                                <div class="bg-light d-flex align-items-center justify-content-center mb-2"
                                     style="width: 150px; height: 150px; margin: 0 auto;">
                                    <i class="fas fa-user fa-4x text-secondary"></i>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-9">
                            <h4 class="mb-1"><?= $application['first_name'] . ' ' . $application['last_name'] ?></h4>
                            <p class="text-muted mb-2">
                                <i class="fas fa-briefcase me-1"></i>
                                Applied for: <?= esc($position['designation']) ?>
                            </p>

                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Gender:</strong> <?= $application['gender'] ?? 'Not specified' ?></p>
                                    <p class="mb-1"><strong>Date of Birth:</strong> <?= $application['date_of_birth'] ? date('d M Y', strtotime($application['date_of_birth'])) : 'Not specified' ?></p>
                                    <p class="mb-1"><strong>Marital Status:</strong> <?= $application['marital_status'] ?? 'Not specified' ?></p>
                                    <?php if (!empty($application['date_of_marriage'])): ?>
                                        <p class="mb-1"><strong>Date of Marriage:</strong> <?= date('d M Y', strtotime($application['date_of_marriage'])) ?></p>
                                    <?php endif; ?>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Citizenship:</strong> <?= $application['citizenship'] ?? 'Not specified' ?></p>
                                    <p class="mb-1"><strong>Place of Origin:</strong> <?= $application['place_of_origin'] ?? 'Not specified' ?></p>
                                    <?php if (!empty($application['spouse_employer'])): ?>
                                        <p class="mb-1"><strong>Spouse Employer:</strong> <?= esc($application['spouse_employer']) ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="card mb-3">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">Contact Information</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Email:</strong> <?= esc($applicant['email'] ?? 'Not provided') ?></p>
                                    <p class="mb-1"><strong>Phone:</strong> <?= esc($application['contact_details'] ?? 'Not provided') ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Address:</strong> <?= esc($application['location_address'] ?? 'Not provided') ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Current Employment -->
                    <div class="card mb-3">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">Current Employment</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Current Employer:</strong> <?= $application['current_employer'] ?? 'Not provided' ?></p>
                                    <p class="mb-1"><strong>Current Position:</strong> <?= $application['current_position'] ?? 'Not provided' ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p class="mb-1"><strong>Current Salary:</strong> <?= $application['current_salary'] ?? 'Not provided' ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Identification Numbers -->
                    <?php if (!empty($application['id_numbers'])): ?>
                        <?php $idNumbers = json_decode($application['id_numbers'], true); ?>
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">Identification Numbers</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php if (!empty($idNumbers['national_id'])): ?>
                                        <div class="col-md-4">
                                            <p class="mb-1"><strong>National ID:</strong> <?= esc($idNumbers['national_id']) ?></p>
                                        </div>
                                    <?php endif; ?>
                                    <?php if (!empty($idNumbers['passport'])): ?>
                                        <div class="col-md-4">
                                            <p class="mb-1"><strong>Passport:</strong> <?= esc($idNumbers['passport']) ?></p>
                                        </div>
                                    <?php endif; ?>
                                    <?php if (!empty($idNumbers['drivers_license'])): ?>
                                        <div class="col-md-4">
                                            <p class="mb-1"><strong>Driver's License:</strong> <?= esc($idNumbers['drivers_license']) ?></p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Family Information -->
                    <?php if (!empty($application['children'])): ?>
                        <?php $children = json_decode($application['children'], true); ?>
                        <?php if (!empty($children)): ?>
                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">Family Information</h6>
                                </div>
                                <div class="card-body">
                                    <p class="mb-2"><strong>Children:</strong></p>
                                    <?php foreach ($children as $child): ?>
                                        <div class="ms-3 mb-2">
                                            <p class="mb-1">• <?= esc($child['name']) ?> (Age: <?= esc($child['age']) ?>)</p>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>

                    <!-- Other Information -->
                    <div class="card">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">Additional Information</h6>
                        </div>
                        <div class="card-body">
                            <p class="mb-1"><strong>Criminal Convictions:</strong> <?= esc($application['offence_convicted'] ?? 'Not specified') ?></p>
                            <p class="mb-1"><strong>How did you hear about us:</strong> <?= esc($application['how_did_you_hear_about_us'] ?? 'Not specified') ?></p>
                            <p class="mb-1"><strong>Application Status:</strong>
                                <span class="badge bg-<?= $application['application_status'] === 'pending_prescreen' ? 'warning' : 'info' ?>">
                                    <?= ucfirst(str_replace('_', ' ', $application['application_status'] ?? 'Unknown')) ?>
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Education History -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>Education History</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($education)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>No education history provided.
                        </div>
                    <?php else: ?>
                        <?php foreach ($education as $edu): ?>
                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">
                                        <?= esc($edu['institution']) ?>
                                        <span class="badge bg-secondary float-end">
                                            <?= esc($edu['education_level'] ?? $edu['course'] ?? 'N/A') ?>
                                        </span>
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-9">
                                            <p class="mb-1"><strong>Course:</strong> <?= esc($edu['course'] ?? 'Not specified') ?></p>
                                            <?php if (!empty($edu['units'])): ?>
                                                <?php $units = is_string($edu['units']) ? json_decode($edu['units'], true) : $edu['units']; ?>
                                                <?php if (!empty($units) && is_array($units)): ?>
                                                    <p class="mb-1"><strong>Units/Subjects:</strong></p>
                                                    <div class="ms-3">
                                                        <?php foreach (array_slice($units, 0, 5) as $unit): ?>
                                                            <span class="badge bg-light text-dark me-1 mb-1"><?= esc($unit) ?></span>
                                                        <?php endforeach; ?>
                                                        <?php if (count($units) > 5): ?>
                                                            <span class="text-muted small">... and <?= count($units) - 5 ?> more</span>
                                                        <?php endif; ?>
                                                    </div>
                                                <?php endif; ?>
                                            <?php endif; ?>
                                        </div>
                                        <div class="col-md-3 text-end">
                                            <p class="mb-0 text-muted small">
                                                <?= !empty($edu['date_from']) ? date('M Y', strtotime($edu['date_from'])) : ($edu['start_year'] ?? 'N/A') ?> -
                                                <?= !empty($edu['date_to']) ? date('M Y', strtotime($edu['date_to'])) : ($edu['end_year'] ?? 'N/A') ?>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Work Experience -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-briefcase me-2"></i>Work Experience</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($experiences)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>No work experience provided.
                        </div>
                    <?php else: ?>
                        <?php foreach ($experiences as $exp): ?>
                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0"><?= esc($exp['position']) ?> at <?= esc($exp['employer'] ?? $exp['organization'] ?? 'Unknown') ?></h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-9">
                                            <?php if (!empty($exp['work_description'])): ?>
                                                <p class="mb-2"><strong>Work Description:</strong></p>
                                                <p class="mb-2 text-muted"><?= nl2br(esc($exp['work_description'])) ?></p>
                                            <?php endif; ?>

                                            <?php if (!empty($exp['achievements'])): ?>
                                                <p class="mb-1"><strong>Key Achievements:</strong></p>
                                                <div class="ms-3 mb-2">
                                                    <?php
                                                    $achievements = explode(',', $exp['achievements']);
                                                    foreach ($achievements as $achievement):
                                                    ?>
                                                        <p class="mb-1">• <?= esc(trim($achievement)) ?></p>
                                                    <?php endforeach; ?>
                                                </div>
                                            <?php endif; ?>

                                            <?php if (!empty($exp['employer_contacts_address'])): ?>
                                                <p class="mb-1"><strong>Employer Contact:</strong></p>
                                                <div class="ms-3 mb-2">
                                                    <small class="text-muted"><?= nl2br(esc($exp['employer_contacts_address'])) ?></small>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="col-md-3 text-end">
                                            <p class="mb-0 text-muted small">
                                                <?= date('M Y', strtotime($exp['date_from'] ?? $exp['start_date'])) ?> -
                                                <?= !empty($exp['date_to'] ?? $exp['end_date']) ? date('M Y', strtotime($exp['date_to'] ?? $exp['end_date'])) : 'Present' ?>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Documents -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-file-alt me-2"></i>Documents</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($files)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>No document files uploaded.
                        </div>
                    <?php else: ?>
                        <?php foreach ($files as $file): ?>
                            <div class="card mb-3">
                                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">
                                        <i class="fas fa-file-pdf me-2 text-danger"></i>
                                        <?= esc($file['file_title'] ?? $file['file_name'] ?? 'Unknown File') ?>
                                    </h6>
                                    <div>
                                        <a href="<?= base_url('public' . $file['file_path']) ?>" class="btn btn-sm btn-primary me-1" target="_blank">
                                            <i class="fas fa-eye me-1"></i> View
                                        </a>
                                        <a href="<?= base_url('public' . $file['file_path']) ?>" class="btn btn-sm btn-success" download>
                                            <i class="fas fa-download me-1"></i> Download
                                        </a>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <?php if (!empty($file['file_description'])): ?>
                                        <p class="mb-2"><strong>Description:</strong> <?= esc($file['file_description']) ?></p>
                                    <?php endif; ?>

                                    <?php if (!empty($file['extracted_texts'])): ?>
                                        <p class="mb-1"><strong>Content Preview:</strong></p>
                                        <div class="bg-light p-2 rounded">
                                            <small class="text-muted">
                                                <?= esc(substr($file['extracted_texts'], 0, 200)) ?>
                                                <?= strlen($file['extracted_texts']) > 200 ? '...' : '' ?>
                                            </small>
                                        </div>
                                    <?php endif; ?>

                                    <div class="row mt-2">
                                        <div class="col-md-6">
                                            <small class="text-muted">
                                                <i class="fas fa-calendar me-1"></i>
                                                Uploaded: <?= date('d M Y H:i', strtotime($file['created_at'] ?? $file['uploaded_at'])) ?>
                                            </small>
                                        </div>
                                        <div class="col-md-6 text-end">
                                            <small class="text-muted">
                                                <i class="fas fa-folder me-1"></i>
                                                <?= esc(basename($file['file_path'])) ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Publications -->
            <?php if (!empty($application['publications'])): ?>
                <?php $publications = json_decode($application['publications'], true); ?>
                <?php if (!empty($publications) && is_array($publications)): ?>
                    <div class="card mb-4">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0"><i class="fas fa-book me-2"></i>Publications</h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($publications as $publication): ?>
                                <div class="mb-2">
                                    <p class="mb-1">• <?= esc($publication) ?></p>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endif; ?>

            <!-- Awards and Recognition -->
            <?php if (!empty($application['awards'])): ?>
                <?php $awards = json_decode($application['awards'], true); ?>
                <?php if (!empty($awards) && is_array($awards)): ?>
                    <div class="card mb-4">
                        <div class="card-header bg-warning text-dark">
                            <h5 class="mb-0"><i class="fas fa-trophy me-2"></i>Awards and Recognition</h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($awards as $award): ?>
                                <div class="mb-2">
                                    <p class="mb-1">🏆 <?= esc($award) ?></p>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endif; ?>

            <!-- Referees -->
            <?php if (!empty($application['referees'])): ?>
                <?php $referees = json_decode($application['referees'], true); ?>
                <?php if (!empty($referees) && is_array($referees)): ?>
                    <div class="card mb-4">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="fas fa-users me-2"></i>Referees</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php foreach ($referees as $referee): ?>
                                    <div class="col-md-6 mb-3">
                                        <div class="card">
                                            <div class="card-body">
                                                <h6 class="card-title"><?= esc($referee['name']) ?></h6>
                                                <p class="card-text">
                                                    <strong>Position:</strong> <?= esc($referee['position']) ?><br>
                                                    <strong>Organization:</strong> <?= esc($referee['organization']) ?><br>
                                                    <strong>Relationship:</strong> <?= esc($referee['relationship']) ?><br>
                                                    <strong>Phone:</strong> <?= esc($referee['phone']) ?><br>
                                                    <strong>Email:</strong> <?= esc($referee['email']) ?>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>

        <!-- Pre-Screening Form Section (Right side) -->
        <div class="col-lg-4">
            <!-- Pre-Screening Form -->
            <form id="preScreeningForm" method="post" action="<?= base_url('application_pre_screening/save/' . $application['id']) ?>">
                <?= csrf_field() ?>

                <!-- Display validation errors if any -->
                <?php if (session()->has('errors')): ?>
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            <?php foreach (session('errors') as $error): ?>
                                <li><?= esc($error) ?></li>
                            <?php endforeach ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <!-- Display success message if any -->
                <?php if (session()->has('success')): ?>
                    <div class="alert alert-success">
                        <?= session('success') ?>
                    </div>
                <?php endif; ?>

                <div class="card mb-4 shadow-sm">
                    <div class="card-header bg-warning d-flex justify-content-between align-items-center py-3">
                        <h5 class="mb-0 text-dark">
                            <i class="fas fa-clipboard-check me-2"></i>Pre-Screening Form
                        </h5>
                        <?php if (!empty($application['pre_screened'])): ?>
                            <span class="badge bg-success">
                                <i class="fas fa-check me-1"></i>Pre-Screened
                            </span>
                        <?php endif; ?>
                    </div>
                    <div class="card-body">
                        <!-- Pre-screening status selection -->
                        <div class="mb-4">
                            <label for="preScreenStatus" class="form-label fw-bold">Pre-Screening Status <span class="text-danger">*</span></label>
                            <select class="form-select form-select-lg" id="preScreenStatus" name="status" required>
                                <option value="">Select Status</option>
                                <option value="passed" <?= ($application['pre_screened_status'] ?? '') === 'passed' ? 'selected' : '' ?>>
                                    Passed
                                </option>
                                <option value="failed" <?= ($application['pre_screened_status'] ?? '') === 'failed' ? 'selected' : '' ?>>
                                    Failed
                                </option>
                                <option value="pending" <?= ($application['pre_screened_status'] ?? '') === 'pending' ? 'selected' : '' ?>>
                                    Pending Additional Info
                                </option>
                            </select>
                        </div>

                        <!-- Pre-screening remarks -->
                        <div class="mb-4">
                            <label for="remarks" class="form-label fw-bold">Remarks</label>
                            <textarea class="form-control" id="remarks" name="remarks" rows="4"
                                placeholder="Enter detailed remarks about the pre-screening decision..."><?= $application['pre_screened_remarks'] ?? '' ?></textarea>
                        </div>
                    </div>
                </div>

                <!-- Pre-Screening Criteria Checklist -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-info text-white py-3">
                        <h5 class="mb-0">
                            <i class="fas fa-tasks me-2"></i>Pre-Screening Criteria
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($preScreenCriteria)): ?>
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                No pre-screening criteria defined for this exercise.
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info mb-4">
                                <i class="fas fa-info-circle me-2"></i> Check each criteria item that the applicant meets.
                            </div>

                            <!-- Progress bar for criteria completion -->
                            <?php
                            // Parse existing results if available
                            $existingResults = [];
                            $metCriteria = 0;
                            $totalCriteria = count($preScreenCriteria);

                            if (!empty($application['pre_screened_criteria_results'])) {
                                $existingResults = json_decode($application['pre_screened_criteria_results'], true) ?? [];
                                foreach ($existingResults as $result) {
                                    if (!empty($result['met'])) {
                                        $metCriteria++;
                                    }
                                }
                            }

                            $percentage = $totalCriteria > 0 ? round(($metCriteria / $totalCriteria) * 100) : 0;
                            ?>

                            <div class="mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="small text-muted">Criteria Progress</span>
                                    <span class="small text-muted"><?= $metCriteria ?>/<?= $totalCriteria ?> completed</span>
                                </div>
                                <div class="progress" style="height: 10px;">
                                    <div id="criteriaProgress" class="progress-bar bg-success" role="progressbar"
                                        style="width: <?= $percentage ?>%;" aria-valuenow="<?= $percentage ?>"
                                        aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>

                            <!-- Criteria list -->
                            <div id="criteriaList">
                                <?php foreach ($preScreenCriteria as $index => $criteria): ?>
                                    <?php
                                    // Check if we have a result for this criteria
                                    $isChecked = false;
                                    $criteriaRemarks = '';

                                    if (!empty($existingResults)) {
                                        foreach ($existingResults as $result) {
                                            if (isset($result['criteriaIndex']) && $result['criteriaIndex'] == $index) {
                                                $isChecked = $result['met'] ?? false;
                                                $criteriaRemarks = $result['remarks'] ?? '';
                                                break;
                                            }
                                        }
                                    }
                                    ?>
                                    <div class="card mb-3 criteria-item <?= $isChecked ? 'border-success' : '' ?>">
                                        <div class="card-header bg-light py-3">
                                            <div class="form-check">
                                                <input class="form-check-input criteria-checkbox" type="checkbox"
                                                    id="criteria<?= $index ?>" name="criteria_met[<?= $index ?>]"
                                                    <?= $isChecked ? 'checked' : '' ?>>
                                                <input type="hidden" name="criteria_index[<?= $index ?>]" value="<?= $index ?>">
                                                <label class="form-check-label fw-bold" for="criteria<?= $index ?>">
                                                    <?= esc($criteria['name']) ?>
                                                </label>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <?php if (!empty($criteria['description'])): ?>
                                                <p class="text-muted mb-3"><?= esc($criteria['description']) ?></p>
                                            <?php endif; ?>

                                            <div class="mb-0">
                                                <label for="criteriaRemarks<?= $index ?>" class="form-label small">Remarks</label>
                                                <textarea class="form-control form-control-sm"
                                                    id="criteriaRemarks<?= $index ?>" name="criteria_remarks[<?= $index ?>]"
                                                    rows="2" placeholder="Add specific remarks for this criteria..."><?= esc($criteriaRemarks) ?></textarea>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>

                        <!-- Submit button -->
                        <div class="d-grid gap-2 mt-4">
                            <button type="submit" id="savePreScreeningBtn" class="btn btn-primary btn-lg">
                                <i class="fas fa-save me-2"></i> Save Pre-Screening
                            </button>
                        </div>
                    </div>
                </div>
            </form>

            <!-- Pre-Screening History Section -->
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">Pre-Screening History</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Date</th>
                                    <th>Status</th>
                                    <th>Remarks</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (!empty($application['pre_screened'])): ?>
                                <tr>
                                    <td><?= date('M d, Y H:i', strtotime($application['pre_screened'])) ?></td>
                                    <td>
                                        <span class="badge <?= $application['pre_screened_status'] === 'passed' ? 'bg-success' :
                                            ($application['pre_screened_status'] === 'failed' ? 'bg-danger' : 'bg-warning') ?>">
                                            <?= ucfirst($application['pre_screened_status']) ?>
                                        </span>
                                    </td>
                                    <td><?= nl2br(esc($application['pre_screened_remarks'])) ?></td>
                                </tr>
                                <?php else: ?>
                                <tr>
                                    <td colspan="3" class="text-center">No pre-screening history available</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Add visual feedback when checkboxes are changed
    $('.criteria-checkbox').change(function() {
        const $card = $(this).closest('.criteria-item');
        if ($(this).is(':checked')) {
            $card.addClass('border-success').removeClass('border-danger');
        } else {
            $card.removeClass('border-success');
        }

        // Update the progress bar
        updateProgressBar();
    });

    // Update the text on the save button based on the selected status
    $('#preScreenStatus').change(function() {
        const status = $(this).val();
        let buttonText = 'Save Pre-Screening';

        if (status === 'passed') {
            buttonText = 'Mark as Passed';
        } else if (status === 'failed') {
            buttonText = 'Mark as Failed';
        } else if (status === 'pending') {
            buttonText = 'Mark as Pending';
        }

        $('#savePreScreeningBtn').html(`<i class="fas fa-save me-2"></i> ${buttonText}`);
    });

    // Function to update the progress bar
    function updateProgressBar() {
        const total = $('.criteria-checkbox').length;
        const checked = $('.criteria-checkbox:checked').length;
        const percentage = (checked / total) * 100;

        $('#criteriaProgress').css('width', `${percentage}%`).attr('aria-valuenow', percentage);
        $('.small.text-muted:last').text(`${checked}/${total} completed`);
    }
});
</script>

<style>
/* Styling for criteria items */
.criteria-item {
    transition: all .3s ease-in-out;
}

.criteria-item:hover {
    box-shadow: 0 .5rem 1rem rgba(0,0,0,.15)!important;
    transform: translateY(-2px);
}

.criteria-checkbox:checked + label {
    color: #198754;
}
</style>
<?= $this->endSection() ?>