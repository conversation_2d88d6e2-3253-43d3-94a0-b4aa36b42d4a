<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('profile_applications_exercise') ?>">Exercises</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('profile_applications_exercise/exercise/' . $position['exercise_id'] . '/positions') ?>">
                            <?= esc($position['exercise_name']) ?>
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?= esc($position['designation']) ?> Profile
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Header Section -->
    <div class="row mb-3">
        <div class="col-md-8">
            <h2><i class="fas fa-id-badge me-2"></i><?= esc($position['designation']) ?></h2>
            <p class="text-muted">
                Position: <strong><?= esc($position['position_reference']) ?></strong> |
                Exercise: <strong><?= esc($position['exercise_name']) ?></strong>
            </p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= base_url('profile_applications_exercise/exercise/' . $position['exercise_id'] . '/positions') ?>" class="btn btn-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i> Back to Positions
            </a>
            <button class="btn btn-primary" onclick="window.print()">
                <i class="fas fa-print me-1"></i> Print
            </button>
        </div>
    </div>

    <!-- Position Information Card -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card border-left-primary">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-briefcase me-2"></i>
                        <?= esc($position['designation']) ?>
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <strong>Position No:</strong><br>
                            <?= esc($position['position_reference']) ?>
                        </div>
                        <div class="col-md-3">
                            <strong>Position Group:</strong><br>
                            <?= esc($position['position_group_name']) ?>
                        </div>
                        <div class="col-md-3">
                            <strong>Classification:</strong><br>
                            <?= esc($position['classification']) ?>
                        </div>
                        <div class="col-md-3">
                            <strong>Salary Range:</strong><br>
                            <?= esc($position['award']) ?>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <strong>Location:</strong><br>
                            <?= esc($position['location']) ?>
                        </div>
                        <div class="col-md-6">
                            <strong>Required Qualifications:</strong><br>
                            <?= esc($position['qualifications']) ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Applicant Profiles Table -->
    <div class="card">
        <div class="card-header bg-white py-3">
            <h5 class="mb-0">
                <i class="fas fa-users me-2"></i>SHORT LIST APPLICANT PROFILE
            </h5>
        </div>
        <div class="card-body">
            <?php if (empty($applicant_profiles)): ?>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>No applicant profiles found for this position.
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-sm">
                        <thead class="table-light">
                            <tr>
                                <th rowspan="2" class="text-center align-middle" style="width: 15%;">
                                    <strong>RECRUITMENT INFORMATION</strong>
                                </th>
                                <th colspan="3" class="text-center">
                                    <strong>APPLICANT PROFILE</strong>
                                </th>
                            </tr>
                            <tr>
                                <?php foreach ($applicant_profiles as $profile): ?>
                                    <th class="text-center" style="width: 28.33%;">
                                        <strong>Applicant <?= $profile['id'] ?></strong>
                                    </th>
                                <?php endforeach; ?>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Name -->
                            <tr>
                                <td><strong>Name:</strong></td>
                                <?php foreach ($applicant_profiles as $profile): ?>
                                    <td><?= esc($profile['name']) ?></td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Sex -->
                            <tr>
                                <td><strong>Sex:</strong></td>
                                <?php foreach ($applicant_profiles as $profile): ?>
                                    <td><?= esc($profile['sex']) ?></td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Age -->
                            <tr>
                                <td><strong>Age:</strong></td>
                                <?php foreach ($applicant_profiles as $profile): ?>
                                    <td><?= esc($profile['age']) ?> years</td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Place of Origin -->
                            <tr>
                                <td><strong>Place of Origin:</strong></td>
                                <?php foreach ($applicant_profiles as $profile): ?>
                                    <td><?= esc($profile['place_origin']) ?></td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Address/Location -->
                            <tr>
                                <td><strong>Address/Location:</strong></td>
                                <?php foreach ($applicant_profiles as $profile): ?>
                                    <td><?= esc($profile['address_location']) ?></td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Contact Details -->
                            <tr>
                                <td><strong>Contact Details:</strong></td>
                                <?php foreach ($applicant_profiles as $profile): ?>
                                    <td><?= esc($profile['contact_details']) ?></td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- NID Number -->
                            <tr>
                                <td><strong>NID Number:</strong></td>
                                <?php foreach ($applicant_profiles as $profile): ?>
                                    <td><?= esc($profile['nid_number']) ?></td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Current Employer -->
                            <tr>
                                <td><strong>Current Employer:</strong></td>
                                <?php foreach ($applicant_profiles as $profile): ?>
                                    <td><?= esc($profile['current_employer']) ?></td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Current Position -->
                            <tr>
                                <td><strong>Current Position:</strong></td>
                                <?php foreach ($applicant_profiles as $profile): ?>
                                    <td><?= esc($profile['current_position']) ?></td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Qualifications -->
                            <tr>
                                <td><strong>Qualifications:</strong></td>
                                <?php foreach ($applicant_profiles as $profile): ?>
                                    <td style="max-width: 200px; word-wrap: break-word;">
                                        <?= nl2br(esc($profile['qualification_text'])) ?>
                                    </td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Other Training -->
                            <tr>
                                <td><strong>Other Training:</strong></td>
                                <?php foreach ($applicant_profiles as $profile): ?>
                                    <td style="max-width: 200px; word-wrap: break-word;">
                                        <?= nl2br(esc($profile['other_trainings'])) ?>
                                    </td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Knowledge -->
                            <tr>
                                <td><strong>Knowledge:</strong></td>
                                <?php foreach ($applicant_profiles as $profile): ?>
                                    <td style="max-width: 200px; word-wrap: break-word;">
                                        <?= nl2br(esc($profile['knowledge'])) ?>
                                    </td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Skills/Competencies -->
                            <tr>
                                <td><strong>Skills/Competencies:</strong></td>
                                <?php foreach ($applicant_profiles as $profile): ?>
                                    <td style="max-width: 200px; word-wrap: break-word;">
                                        <?= nl2br(esc($profile['skills_competencies'])) ?>
                                    </td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Relevant Job Experience -->
                            <tr>
                                <td><strong>Relevant Job Experience:</strong></td>
                                <?php foreach ($applicant_profiles as $profile): ?>
                                    <td style="max-width: 200px; word-wrap: break-word;">
                                        <?= nl2br(esc($profile['job_experiences'])) ?>
                                    </td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Publications -->
                            <tr>
                                <td><strong>Publications:</strong></td>
                                <?php foreach ($applicant_profiles as $profile): ?>
                                    <td style="max-width: 200px; word-wrap: break-word;">
                                        <?= !empty($profile['publications']) ? nl2br(esc($profile['publications'])) : 'None' ?>
                                    </td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Awards -->
                            <tr>
                                <td><strong>Awards:</strong></td>
                                <?php foreach ($applicant_profiles as $profile): ?>
                                    <td style="max-width: 200px; word-wrap: break-word;">
                                        <?= !empty($profile['awards']) ? nl2br(esc($profile['awards'])) : 'None' ?>
                                    </td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Referees -->
                            <tr>
                                <td><strong>Referees:</strong></td>
                                <?php foreach ($applicant_profiles as $profile): ?>
                                    <td style="max-width: 200px; word-wrap: break-word;">
                                        <?= nl2br(esc($profile['referees'])) ?>
                                    </td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Comments -->
                            <tr>
                                <td><strong>Comments:</strong></td>
                                <?php foreach ($applicant_profiles as $profile): ?>
                                    <td style="max-width: 200px; word-wrap: break-word;">
                                        <?= nl2br(esc($profile['comments'])) ?>
                                    </td>
                                <?php endforeach; ?>
                            </tr>

                            <!-- Remarks -->
                            <tr class="table-warning">
                                <td><strong>Remarks:</strong></td>
                                <?php foreach ($applicant_profiles as $profile): ?>
                                    <td style="max-width: 200px; word-wrap: break-word;">
                                        <strong><?= nl2br(esc($profile['remarks'])) ?></strong>
                                    </td>
                                <?php endforeach; ?>
                            </tr>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Scoring Information Table -->
    <div class="card mt-4">
        <div class="card-header bg-white py-3">
            <h5 class="mb-0">
                <i class="fas fa-chart-bar me-2"></i>SCORING INFORMATION
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-sm">
                    <thead class="table-light">
                        <tr>
                            <th style="width: 20%;">Criteria</th>
                            <?php foreach ($applicant_profiles as $profile): ?>
                                <th class="text-center" style="width: 20%;">Applicant <?= $profile['id'] ?></th>
                            <?php endforeach; ?>
                            <th class="text-center" style="width: 20%;">Out of</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($scoring_data['criteria'] as $criteria => $data): ?>
                            <tr>
                                <td><strong><?= esc($criteria) ?></strong></td>
                                <?php foreach ($data['scores'] as $score): ?>
                                    <td class="text-center"><?= $score ?></td>
                                <?php endforeach; ?>
                                <td class="text-center"><?= $data['out_of'] ?></td>
                            </tr>
                        <?php endforeach; ?>
                        <tr class="table-secondary">
                            <td><strong>Total</strong></td>
                            <?php foreach ($scoring_data['totals'] as $total): ?>
                                <td class="text-center"><strong><?= $total ?></strong></td>
                            <?php endforeach; ?>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Short List and Eliminated Section -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-success text-white py-3">
                    <h5 class="mb-0">
                        <i class="fas fa-list-ol me-2"></i>SHORT LIST IN ORDER OF PREFERENCE
                    </h5>
                </div>
                <div class="card-body">
                    <ol class="list-unstyled mb-0">
                        <?php foreach ($short_list as $index => $applicant): ?>
                            <li><?= ($index + 1) ?>. <?= esc($applicant) ?></li>
                        <?php endforeach; ?>
                    </ol>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-danger text-white py-3">
                    <h5 class="mb-0">
                        <i class="fas fa-times-circle me-2"></i>ELIMINATED
                    </h5>
                </div>
                <div class="card-body">
                    <ol class="list-unstyled mb-0">
                        <?php if (!empty($eliminated)): ?>
                            <?php foreach ($eliminated as $index => $applicant): ?>
                                <li><?= ($index + 1) ?>. <?= esc($applicant) ?></li>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <li class="text-muted">No applicants eliminated</li>
                        <?php endif; ?>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="Applicant profiles pagination">
                <ul class="pagination justify-content-center">
                    <li class="page-item disabled">
                        <a class="page-link" href="#" tabindex="-1" aria-disabled="true">Previous</a>
                    </li>
                    <li class="page-item active" aria-current="page">
                        <a class="page-link" href="#">1</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="#">2</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="#">3</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="#">4</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="#">5</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="#">6</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="#">7</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="#">Next</a>
                    </li>
                </ul>
            </nav>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    console.log('Position Profile view loaded');

    // Print functionality
    window.print = function() {
        window.print();
    };

    // Pagination functionality
    $('.pagination .page-link').on('click', function(e) {
        e.preventDefault();

        // Remove active class from all items
        $('.pagination .page-item').removeClass('active');

        // Add active class to clicked item (if not Previous/Next)
        if (!$(this).parent().hasClass('disabled') &&
            $(this).text() !== 'Previous' &&
            $(this).text() !== 'Next') {
            $(this).parent().addClass('active');
        }

        // Handle Previous/Next logic
        if ($(this).text() === 'Next') {
            // Logic for next page
            console.log('Next page clicked');
        } else if ($(this).text() === 'Previous') {
            // Logic for previous page
            console.log('Previous page clicked');
        } else {
            // Logic for specific page number
            console.log('Page ' + $(this).text() + ' clicked');
        }
    });
});
</script>
<?= $this->endSection() ?>
